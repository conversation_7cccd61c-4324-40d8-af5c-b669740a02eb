2025-06-03 03:17:53.620 [info] [main] Log level: Info
2025-06-03 03:17:53.620 [info] [main] Validating found git in: "git"
2025-06-03 03:17:53.620 [info] [main] Using git "2.47.2" from "git"
2025-06-03 03:17:53.620 [info] [Model][doInitialScan] Initial repository scan started
2025-06-03 03:17:53.620 [info] > git rev-parse --show-toplevel [3ms]
2025-06-03 03:17:53.620 [info] > git rev-parse --git-dir --git-common-dir [150ms]
2025-06-03 03:17:53.620 [info] [Model][openRepository] Opened repository (path): /home/<USER>/workspace
2025-06-03 03:17:53.620 [info] [Model][openRepository] Opened repository (real path): /home/<USER>/workspace
2025-06-03 03:17:53.620 [info] > git config --get commit.template [4ms]
2025-06-03 03:17:53.620 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-03 03:17:53.620 [info] > git status -z -uall [10ms]
2025-06-03 03:17:53.620 [info] > git rev-parse --show-toplevel [465ms]
2025-06-03 03:17:53.620 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [10ms]
2025-06-03 03:17:53.620 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [12ms]
2025-06-03 03:17:53.620 [info] > git rev-parse --show-toplevel [54ms]
2025-06-03 03:17:53.620 [info] > git config --get commit.template [13ms]
2025-06-03 03:17:53.620 [info] > git rev-parse --show-toplevel [1ms]
2025-06-03 03:17:53.620 [info] > git config --get --local branch.main.vscode-merge-base [7ms]
2025-06-03 03:17:53.620 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-06-03 03:17:53.620 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/origin/main refs/remotes/origin/main [11ms]
2025-06-03 03:17:53.620 [info] > git rev-parse --show-toplevel [11ms]
2025-06-03 03:17:53.620 [info] > git merge-base refs/heads/main refs/remotes/origin/main [7ms]
2025-06-03 03:17:53.620 [info] > git status -z -uall [6ms]
2025-06-03 03:17:53.620 [info] > git rev-parse --show-toplevel [16ms]
2025-06-03 03:17:53.620 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [30ms]
2025-06-03 03:17:53.620 [info] > git diff --name-status -z --diff-filter=ADMR 435a5ff9421201dec9d4e684da4764850c24096a...refs/remotes/origin/main [51ms]
2025-06-03 03:17:53.620 [info] > git merge-base refs/heads/main refs/remotes/origin/main [2ms]
2025-06-03 03:17:53.620 [info] > git rev-parse --show-toplevel [21ms]
2025-06-03 03:17:53.620 [info] > git diff --name-status -z --diff-filter=ADMR 435a5ff9421201dec9d4e684da4764850c24096a...refs/remotes/origin/main [8ms]
2025-06-03 03:17:53.620 [info] > git rev-parse --show-toplevel [1ms]
2025-06-03 03:17:53.620 [info] > git rev-parse --show-toplevel [3ms]
2025-06-03 03:17:53.620 [info] > git rev-parse --show-toplevel [2ms]
2025-06-03 03:17:53.620 [info] > git rev-parse --show-toplevel [2ms]
2025-06-03 03:17:53.620 [info] > git rev-parse --show-toplevel [362ms]
2025-06-03 03:17:53.620 [info] > git rev-parse --show-toplevel [5ms]
2025-06-03 03:17:53.620 [info] > git rev-parse --show-toplevel [258ms]
2025-06-03 03:17:53.620 [info] > git fetch [581ms]
2025-06-03 03:17:53.620 [info] Missing or invalid credentials.
Skip silent fetch commands
Missing or invalid credentials.
Skip silent fetch commands
remote: Repository not found.
fatal: Authentication failed for 'https://github.com/Chewy42/ChewyAI/'
2025-06-03 03:17:53.620 [info] > git config --get commit.template [14ms]
2025-06-03 03:17:53.620 [info] > git rev-parse --show-toplevel [21ms]
2025-06-03 03:17:53.620 [info] [Model][doInitialScan] Initial repository scan completed - repositories (1), closed repositories (0), parent repositories (0), unsafe repositories (0)
2025-06-03 03:17:53.794 [info] > git config --get commit.template [163ms]
2025-06-03 03:17:53.889 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [205ms]
2025-06-03 03:17:53.951 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [52ms]
2025-06-03 03:17:54.347 [info] > git status -z -uall [21ms]
2025-06-03 03:17:54.350 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [13ms]
2025-06-03 03:17:54.393 [info] > git config --get commit.template [19ms]
2025-06-03 03:17:54.428 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-03 03:17:54.994 [info] > git status -z -uall [548ms]
2025-06-03 03:17:54.995 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [534ms]
2025-06-03 03:17:57.316 [info] > git config --get --local branch.main.github-pr-owner-number [212ms]
2025-06-03 03:17:57.316 [warning] [Git][config] git config failed: Failed to execute git
2025-06-03 03:20:05.019 [info] > git show --textconv :client/src/components/ui/toast.tsx [15ms]
2025-06-03 03:20:05.022 [info] > git ls-files --stage -- client/src/components/ui/toast.tsx [6ms]
2025-06-03 03:20:05.056 [info] > git cat-file -s a822477534192c4df5073e4015f7461e739d3344 [4ms]
2025-06-03 03:20:05.186 [info] > git check-ignore -v -z --stdin [3ms]
2025-06-03 03:23:29.061 [info] > git config --get commit.template [13ms]
2025-06-03 03:23:29.062 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-03 03:23:29.086 [info] > git status -z -uall [11ms]
2025-06-03 03:23:29.087 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 03:23:34.128 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [0ms]
2025-06-03 03:23:34.130 [info] > git config --get commit.template [19ms]
2025-06-03 03:23:34.186 [info] > git status -z -uall [28ms]
2025-06-03 03:23:34.187 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-03 03:23:39.243 [info] > git config --get commit.template [22ms]
2025-06-03 03:23:39.244 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-03 03:23:39.269 [info] > git status -z -uall [11ms]
2025-06-03 03:23:39.269 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-03 03:23:44.290 [info] > git config --get commit.template [8ms]
2025-06-03 03:23:44.291 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-03 03:23:44.305 [info] > git status -z -uall [6ms]
2025-06-03 03:23:44.306 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 03:23:49.327 [info] > git config --get commit.template [7ms]
2025-06-03 03:23:49.328 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-03 03:23:49.345 [info] > git status -z -uall [8ms]
2025-06-03 03:23:49.346 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 03:23:54.368 [info] > git config --get commit.template [7ms]
2025-06-03 03:23:54.369 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-03 03:23:54.383 [info] > git status -z -uall [7ms]
2025-06-03 03:23:54.384 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-03 03:23:59.408 [info] > git config --get commit.template [9ms]
2025-06-03 03:23:59.409 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-03 03:23:59.423 [info] > git status -z -uall [7ms]
2025-06-03 03:23:59.424 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-03 03:24:04.445 [info] > git config --get commit.template [6ms]
2025-06-03 03:24:04.446 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-03 03:24:04.468 [info] > git status -z -uall [9ms]
2025-06-03 03:24:04.469 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-03 03:24:09.487 [info] > git config --get commit.template [2ms]
2025-06-03 03:24:09.498 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-03 03:24:09.514 [info] > git status -z -uall [7ms]
2025-06-03 03:24:09.516 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 03:24:14.536 [info] > git config --get commit.template [6ms]
2025-06-03 03:24:14.537 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-03 03:24:14.553 [info] > git status -z -uall [7ms]
2025-06-03 03:24:14.554 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 03:24:19.634 [info] > git config --get commit.template [63ms]
2025-06-03 03:24:19.635 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [55ms]
2025-06-03 03:24:19.657 [info] > git status -z -uall [10ms]
2025-06-03 03:24:19.658 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-03 03:24:24.680 [info] > git config --get commit.template [7ms]
2025-06-03 03:24:24.681 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-03 03:24:24.699 [info] > git status -z -uall [6ms]
2025-06-03 03:24:24.700 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 03:24:29.725 [info] > git config --get commit.template [10ms]
2025-06-03 03:24:29.727 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-03 03:24:29.745 [info] > git status -z -uall [9ms]
2025-06-03 03:24:29.746 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 03:24:34.768 [info] > git config --get commit.template [3ms]
2025-06-03 03:24:34.778 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-03 03:24:34.792 [info] > git status -z -uall [6ms]
2025-06-03 03:24:34.793 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 03:25:41.285 [info] > git config --get commit.template [6ms]
2025-06-03 03:25:41.285 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-03 03:25:41.297 [info] > git status -z -uall [6ms]
2025-06-03 03:25:41.298 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-03 03:25:46.320 [info] > git config --get commit.template [8ms]
2025-06-03 03:25:46.321 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-03 03:25:46.337 [info] > git status -z -uall [9ms]
2025-06-03 03:25:46.339 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 03:25:51.361 [info] > git config --get commit.template [2ms]
2025-06-03 03:25:51.374 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-03 03:25:51.395 [info] > git status -z -uall [12ms]
2025-06-03 03:25:51.396 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 03:25:56.417 [info] > git config --get commit.template [7ms]
2025-06-03 03:25:56.417 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-03 03:25:56.430 [info] > git status -z -uall [7ms]
2025-06-03 03:25:56.434 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-03 03:26:01.457 [info] > git config --get commit.template [7ms]
2025-06-03 03:26:01.458 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-03 03:26:01.471 [info] > git status -z -uall [6ms]
2025-06-03 03:26:01.472 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-03 03:26:06.496 [info] > git config --get commit.template [9ms]
2025-06-03 03:26:06.497 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-03 03:26:06.514 [info] > git status -z -uall [9ms]
2025-06-03 03:26:06.515 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-03 03:26:11.616 [info] > git config --get commit.template [83ms]
2025-06-03 03:26:11.618 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-03 03:26:11.652 [info] > git status -z -uall [18ms]
2025-06-03 03:26:11.653 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-06-03 03:26:16.676 [info] > git config --get commit.template [2ms]
2025-06-03 03:26:16.699 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-03 03:26:16.718 [info] > git status -z -uall [9ms]
2025-06-03 03:26:16.719 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-03 03:26:21.742 [info] > git config --get commit.template [9ms]
2025-06-03 03:26:21.743 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-03 03:26:21.762 [info] > git status -z -uall [9ms]
2025-06-03 03:26:21.763 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 03:27:00.549 [info] > git config --get commit.template [2ms]
2025-06-03 03:27:00.570 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-03 03:27:00.609 [info] > git status -z -uall [21ms]
2025-06-03 03:27:00.613 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-03 03:27:05.639 [info] > git config --get commit.template [10ms]
2025-06-03 03:27:05.640 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-06-03 03:27:05.668 [info] > git status -z -uall [14ms]
2025-06-03 03:27:05.669 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 03:27:10.882 [info] > git config --get commit.template [4ms]
2025-06-03 03:27:10.967 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [9ms]
2025-06-03 03:27:11.049 [info] > git status -z -uall [37ms]
2025-06-03 03:27:11.052 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-06-03 03:29:40.907 [info] > git config --get commit.template [12ms]
2025-06-03 03:29:40.908 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-03 03:29:40.939 [info] > git status -z -uall [22ms]
2025-06-03 03:29:40.942 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [10ms]
2025-06-03 03:29:45.980 [info] > git config --get commit.template [21ms]
2025-06-03 03:29:45.981 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-03 03:29:46.018 [info] > git status -z -uall [20ms]
2025-06-03 03:29:46.023 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-06-03 03:30:45.013 [info] > git config --get commit.template [9ms]
2025-06-03 03:30:45.014 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-03 03:30:45.031 [info] > git status -z -uall [7ms]
2025-06-03 03:30:45.033 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 03:30:50.073 [info] > git config --get commit.template [13ms]
2025-06-03 03:30:50.074 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-03 03:30:50.093 [info] > git status -z -uall [8ms]
2025-06-03 03:30:50.094 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 03:31:05.679 [info] > git config --get commit.template [8ms]
2025-06-03 03:31:05.680 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-03 03:31:05.694 [info] > git status -z -uall [7ms]
2025-06-03 03:31:05.695 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 03:31:21.469 [info] > git config --get commit.template [7ms]
2025-06-03 03:31:21.470 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-03 03:31:21.491 [info] > git status -z -uall [13ms]
2025-06-03 03:31:21.493 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 03:31:26.554 [info] > git config --get commit.template [35ms]
2025-06-03 03:31:26.556 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-03 03:31:26.622 [info] > git status -z -uall [39ms]
2025-06-03 03:31:26.622 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [8ms]
2025-06-03 03:31:37.697 [info] > git config --get commit.template [8ms]
2025-06-03 03:31:37.697 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-03 03:31:37.712 [info] > git status -z -uall [8ms]
2025-06-03 03:31:37.713 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 03:31:37.756 [info] > git blame --root --incremental 435a5ff9421201dec9d4e684da4764850c24096a -- client/src/components/ui/toast.tsx [3ms]
2025-06-03 03:31:42.952 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [16ms]
2025-06-03 03:31:42.952 [info] > git config --get commit.template [67ms]
2025-06-03 03:31:43.103 [info] > git status -z -uall [107ms]
2025-06-03 03:31:43.103 [info] > git check-ignore -v -z --stdin [58ms]
2025-06-03 03:31:43.104 [info] > git show --textconv :client/src/lib/api.ts [33ms]
2025-06-03 03:31:43.105 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [77ms]
2025-06-03 03:31:43.132 [info] > git ls-files --stage -- client/src/lib/api.ts [35ms]
2025-06-03 03:31:43.177 [info] > git cat-file -s 46737e1c6f82a208a09befa57f1d42140f720549 [11ms]
2025-06-03 03:31:43.485 [info] > git blame --root --incremental 435a5ff9421201dec9d4e684da4764850c24096a -- client/src/lib/api.ts [55ms]
2025-06-03 03:31:48.153 [info] > git config --get commit.template [22ms]
2025-06-03 03:31:48.155 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-03 03:31:48.192 [info] > git status -z -uall [20ms]
2025-06-03 03:31:48.193 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-03 03:31:53.216 [info] > git config --get commit.template [1ms]
2025-06-03 03:31:53.246 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-03 03:31:53.289 [info] > git status -z -uall [19ms]
2025-06-03 03:31:53.290 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 03:31:58.314 [info] > git config --get commit.template [7ms]
2025-06-03 03:31:58.315 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-03 03:31:58.331 [info] > git status -z -uall [10ms]
2025-06-03 03:31:58.333 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 03:32:03.356 [info] > git config --get commit.template [3ms]
2025-06-03 03:32:03.371 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-03 03:32:03.418 [info] > git status -z -uall [32ms]
2025-06-03 03:32:03.422 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-03 03:32:08.481 [info] > git config --get commit.template [19ms]
2025-06-03 03:32:08.482 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-03 03:32:08.506 [info] > git status -z -uall [11ms]
2025-06-03 03:32:08.507 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 03:32:13.535 [info] > git config --get commit.template [12ms]
2025-06-03 03:32:13.536 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-03 03:32:13.553 [info] > git status -z -uall [7ms]
2025-06-03 03:32:13.554 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 03:32:18.584 [info] > git config --get commit.template [12ms]
2025-06-03 03:32:18.584 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-03 03:32:18.606 [info] > git status -z -uall [10ms]
2025-06-03 03:32:18.608 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 03:32:23.635 [info] > git config --get commit.template [10ms]
2025-06-03 03:32:23.636 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-03 03:32:23.651 [info] > git status -z -uall [7ms]
2025-06-03 03:32:23.652 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-03 03:32:29.047 [info] > git config --get commit.template [13ms]
2025-06-03 03:32:29.047 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-03 03:32:29.062 [info] > git status -z -uall [6ms]
2025-06-03 03:32:29.064 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 03:32:35.241 [info] > git config --get commit.template [32ms]
2025-06-03 03:32:35.499 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [238ms]
2025-06-03 03:32:35.531 [info] > git status -z -uall [10ms]
2025-06-03 03:32:35.532 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 03:32:40.554 [info] > git config --get commit.template [7ms]
2025-06-03 03:32:40.555 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-03 03:32:40.570 [info] > git status -z -uall [7ms]
2025-06-03 03:32:40.571 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 03:32:45.664 [info] > git config --get commit.template [11ms]
2025-06-03 03:32:45.665 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-03 03:32:45.684 [info] > git status -z -uall [11ms]
2025-06-03 03:32:45.685 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-03 03:32:50.709 [info] > git config --get commit.template [8ms]
2025-06-03 03:32:50.710 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-03 03:32:50.728 [info] > git status -z -uall [9ms]
2025-06-03 03:32:50.729 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 03:32:55.755 [info] > git config --get commit.template [8ms]
2025-06-03 03:32:55.755 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-03 03:32:55.832 [info] > git status -z -uall [64ms]
2025-06-03 03:32:55.833 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [54ms]
2025-06-03 03:33:00.855 [info] > git config --get commit.template [2ms]
2025-06-03 03:33:00.871 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-03 03:33:00.893 [info] > git status -z -uall [10ms]
2025-06-03 03:33:00.894 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 03:33:05.920 [info] > git config --get commit.template [9ms]
2025-06-03 03:33:05.921 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-03 03:33:05.938 [info] > git status -z -uall [10ms]
2025-06-03 03:33:05.939 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-03 03:33:13.103 [info] > git config --get commit.template [11ms]
2025-06-03 03:33:13.104 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-03 03:33:13.127 [info] > git status -z -uall [11ms]
2025-06-03 03:33:13.128 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-03 03:33:18.148 [info] > git config --get commit.template [2ms]
2025-06-03 03:33:18.159 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-03 03:33:18.182 [info] > git status -z -uall [13ms]
2025-06-03 03:33:18.183 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 03:33:23.217 [info] > git config --get commit.template [15ms]
2025-06-03 03:33:23.225 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [9ms]
2025-06-03 03:33:23.250 [info] > git status -z -uall [11ms]
2025-06-03 03:33:23.251 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-03 03:33:28.279 [info] > git config --get commit.template [7ms]
2025-06-03 03:33:28.279 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-03 03:33:28.292 [info] > git status -z -uall [6ms]
2025-06-03 03:33:28.293 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 03:33:33.322 [info] > git config --get commit.template [12ms]
2025-06-03 03:33:33.322 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-03 03:33:33.349 [info] > git status -z -uall [14ms]
2025-06-03 03:33:33.350 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 03:33:38.535 [info] > git config --get commit.template [51ms]
2025-06-03 03:33:38.540 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [29ms]
2025-06-03 03:33:38.599 [info] > git status -z -uall [33ms]
2025-06-03 03:33:38.605 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [14ms]
2025-06-03 03:33:44.555 [info] > git config --get commit.template [5ms]
2025-06-03 03:33:44.577 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [6ms]
2025-06-03 03:33:44.610 [info] > git status -z -uall [14ms]
2025-06-03 03:33:44.612 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 03:33:49.633 [info] > git config --get commit.template [7ms]
2025-06-03 03:33:49.634 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-03 03:33:49.650 [info] > git status -z -uall [8ms]
2025-06-03 03:33:49.651 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-03 03:33:54.673 [info] > git config --get commit.template [7ms]
2025-06-03 03:33:54.674 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-03 03:33:54.687 [info] > git status -z -uall [6ms]
2025-06-03 03:33:54.688 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-03 03:33:59.718 [info] > git config --get commit.template [12ms]
2025-06-03 03:33:59.718 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-03 03:33:59.749 [info] > git status -z -uall [16ms]
2025-06-03 03:33:59.750 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 03:34:04.820 [info] > git config --get commit.template [40ms]
2025-06-03 03:34:04.823 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-03 03:34:04.871 [info] > git status -z -uall [27ms]
2025-06-03 03:34:04.877 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [9ms]
2025-06-03 03:34:24.522 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-03 03:34:24.522 [info] > git config --get commit.template [18ms]
2025-06-03 03:34:24.555 [info] > git status -z -uall [12ms]
2025-06-03 03:34:24.555 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-03 03:34:32.012 [info] > git config --get commit.template [10ms]
2025-06-03 03:34:32.029 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [18ms]
2025-06-03 03:34:32.060 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [9ms]
2025-06-03 03:34:32.083 [info] > git status -z -uall [41ms]
2025-06-03 03:35:03.458 [info] > git config --get commit.template [2ms]
2025-06-03 03:35:03.471 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-03 03:35:03.485 [info] > git status -z -uall [7ms]
2025-06-03 03:35:03.486 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 03:35:08.514 [info] > git config --get commit.template [11ms]
2025-06-03 03:35:08.515 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-03 03:35:08.534 [info] > git status -z -uall [8ms]
2025-06-03 03:35:08.535 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 03:35:13.563 [info] > git config --get commit.template [11ms]
2025-06-03 03:35:13.564 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-03 03:35:13.584 [info] > git status -z -uall [7ms]
2025-06-03 03:35:13.586 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 03:35:18.618 [info] > git config --get commit.template [12ms]
2025-06-03 03:35:18.619 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-03 03:35:18.637 [info] > git status -z -uall [8ms]
2025-06-03 03:35:18.638 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 03:35:23.659 [info] > git config --get commit.template [7ms]
2025-06-03 03:35:23.660 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-03 03:35:23.672 [info] > git status -z -uall [6ms]
2025-06-03 03:35:23.673 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-03 03:35:28.697 [info] > git config --get commit.template [9ms]
2025-06-03 03:35:28.698 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-03 03:35:28.721 [info] > git status -z -uall [8ms]
2025-06-03 03:35:28.722 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 03:35:33.748 [info] > git config --get commit.template [12ms]
2025-06-03 03:35:33.748 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-03 03:35:33.766 [info] > git status -z -uall [9ms]
2025-06-03 03:35:33.767 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 03:35:38.796 [info] > git config --get commit.template [1ms]
2025-06-03 03:35:38.820 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-03 03:35:38.861 [info] > git status -z -uall [23ms]
2025-06-03 03:35:38.861 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-03 03:37:13.298 [info] > git config --get commit.template [9ms]
2025-06-03 03:37:13.300 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-03 03:37:13.314 [info] > git status -z -uall [7ms]
2025-06-03 03:37:13.315 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-03 03:37:18.345 [info] > git config --get commit.template [12ms]
2025-06-03 03:37:18.346 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-03 03:37:18.365 [info] > git status -z -uall [9ms]
2025-06-03 03:37:18.369 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-03 03:37:23.401 [info] > git config --get commit.template [14ms]
2025-06-03 03:37:23.403 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-03 03:37:23.425 [info] > git status -z -uall [11ms]
2025-06-03 03:37:23.428 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-03 03:37:28.451 [info] > git config --get commit.template [7ms]
2025-06-03 03:37:28.452 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-03 03:37:28.470 [info] > git status -z -uall [11ms]
2025-06-03 03:37:28.471 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 03:37:33.493 [info] > git config --get commit.template [1ms]
2025-06-03 03:37:33.511 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-03 03:37:33.544 [info] > git status -z -uall [17ms]
2025-06-03 03:37:33.545 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 03:37:38.562 [info] > git config --get commit.template [1ms]
2025-06-03 03:37:38.578 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-03 03:37:38.616 [info] > git status -z -uall [17ms]
2025-06-03 03:37:38.617 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-03 03:37:43.657 [info] > git config --get commit.template [17ms]
2025-06-03 03:37:43.657 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-03 03:37:43.690 [info] > git status -z -uall [18ms]
2025-06-03 03:37:43.691 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
