2025-06-03 03:17:53.612 [info] Can't use the Electron fetcher in this environment.
2025-06-03 03:17:53.612 [info] Using the Node fetch fetcher.
2025-06-03 03:17:53.612 [info] Initializing Git extension service.
2025-06-03 03:17:53.612 [info] Successfully activated the vscode.git extension.
2025-06-03 03:17:53.612 [info] Enablement state of the vscode.git extension: true.
2025-06-03 03:17:53.612 [info] Successfully registered Git commit message provider.
2025-06-03 03:17:55.029 [info] Logged in as Chewy42
2025-06-03 03:17:57.358 [info] Got Copilot token for Chewy42
2025-06-03 03:17:57.955 [info] Fetched model metadata in 562ms 065d8eb3-97d0-432d-a2a5-a50c6a6794c8
2025-06-03 03:17:57.969 [info] activationBlocker from 'languageModelAccess' took for 6507ms
2025-06-03 03:17:58.248 [info] copilot token chat_enabled: true, sku: free_educational
2025-06-03 03:17:58.263 [info] Registering default platform agent...
2025-06-03 03:17:58.478 [info] Successfully activated the GitHub.vscode-pull-request-github extension.
2025-06-03 03:17:58.478 [info] [githubTitleAndDescriptionProvider] Initializing GitHub PR title and description provider provider.
2025-06-03 03:17:58.478 [info] Successfully registered GitHub PR title and description provider.
2025-06-03 03:17:58.478 [info] Successfully registered GitHub PR reviewer comments provider.
2025-06-03 03:18:00.180 [info] BYOK: Copilot Chat known models list fetched successfully.
2025-06-03 03:19:25.445 [info] TypeScript server plugin activated.
2025-06-03 03:19:25.445 [info] Registered TypeScript context provider with Copilot inline completions.
2025-06-03 03:33:01.288 [info] Fetched model metadata in 544ms 8070085b-14c4-4160-bb08-ad0472005ea4
